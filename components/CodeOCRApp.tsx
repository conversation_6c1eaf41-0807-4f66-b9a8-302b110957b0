"use client";

import CodeDisplay from "@/components/CodeDisplay";
import ImageUpload from "@/components/ImageUpload";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Check, Loader2, Upload } from "lucide-react";
import { useCallback, useState } from "react";
import { toast } from "react-hot-toast";

interface CodeOCRResponse {
  language: string;
  code: string;
  status: string;
}

export default function CodeOCRApp() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
    setError(null);
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
    setError(null);
  }, []);

  const processImage = async () => {
    if (!selectedFile) {
      toast.error("Please select an image first");
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);

      const response = await fetch("/api/v1/codeocr", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || errorData.detail || "Failed to process image"
        );
      }

      const data: CodeOCRResponse = await response.json();
      setResult(data);
      toast.success("Code extracted successfully!");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to process image";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4">
          <div className="space-y-4">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            {selectedFile && (
              <Button
                onClick={processImage}
                disabled={isProcessing}
                className="w-full"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing Image...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Extract Code
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-400">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Error:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Section */}
      {result && (
        <Card className="shadow-md">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Check className="h-5 w-5 text-green-600" />
                <h2 className="text-lg font-semibold">Extracted Code</h2>
              </div>

              <CodeDisplay code={result.code} language={result.language} />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
