"use client";

import React, { useCallback, useState, useRef } from "react";
import { Upload, X, Image as ImageIcon, FileImage } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "react-hot-toast";

interface ImageUploadProps {
  onFileSelect: (file: File) => void;
  onRemoveFile: () => void;
  selectedFile: File | null;
  disabled?: boolean;
}

const ACCEPTED_TYPES = ["image/png", "image/jpeg", "image/jpg", "image/webp"];
const MAX_SIZE = 5 * 1024 * 1024; // 5MB

export default function ImageUpload({
  onFileSelect,
  onRemoveFile,
  selectedFile,
  disabled = false,
}: ImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    if (!ACCEPTED_TYPES.includes(file.type)) {
      toast.error("Please upload a valid image file (PNG, JPG, JPEG, WebP)");
      return false;
    }

    if (file.size > MAX_SIZE) {
      toast.error("File size must be less than 5MB");
      return false;
    }

    return true;
  };

  const handleFileSelect = useCallback(
    (file: File) => {
      if (!validateFile(file)) return;

      onFileSelect(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    },
    [onFileSelect],
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      if (disabled) return;

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect, disabled],
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragOver(true);
      }
    },
    [disabled],
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect],
  );

  const handleRemove = useCallback(() => {
    onRemoveFile();
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [onRemoveFile]);

  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  // Handle paste from clipboard
  React.useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      if (disabled) return;

      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf("image") !== -1) {
          const file = item.getAsFile();
          if (file) {
            handleFileSelect(file);
            toast.success("Image pasted from clipboard!");
          }
          break;
        }
      }
    };

    document.addEventListener("paste", handlePaste);
    return () => document.removeEventListener("paste", handlePaste);
  }, [handleFileSelect, disabled]);

  if (selectedFile && preview) {
    return (
      <div className="space-y-4">
        <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <img
                src={preview}
                alt="Preview"
                className="w-32 h-32 object-cover rounded-lg border"
              />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {selectedFile.name}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  onClick={handleRemove}
                  variant="outline"
                  size="sm"
                  disabled={disabled}
                  className="flex-shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${
            isDragOver
              ? "border-blue-400 bg-blue-50 dark:bg-blue-900/20"
              : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
          }
          ${disabled ? "opacity-50 cursor-not-allowed" : ""}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={ACCEPTED_TYPES.join(",")}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
              <Upload className="h-8 w-8 text-gray-600 dark:text-gray-400" />
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Upload your code screenshot
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Drag and drop, click to browse, or paste from clipboard (Ctrl+V /
              Cmd+V)
            </p>
          </div>

          <div className="flex items-center justify-center gap-4 text-xs text-gray-400 dark:text-gray-500">
            <span className="flex items-center gap-1">
              <FileImage className="h-3 w-3" />
              PNG, JPG, WebP
            </span>
            <span>•</span>
            <span>Max 5MB</span>
          </div>
        </div>
      </div>

      <div className="text-center">
        <Button
          onClick={handleClick}
          variant="outline"
          disabled={disabled}
          className="w-full sm:w-auto"
        >
          <ImageIcon className="mr-2 h-4 w-4" />
          Choose File
        </Button>
      </div>
    </div>
  );
}
